-- Seed data for AssistantMessageType
-- This script populates the assistant_message_types table with the three required message types

INSERT INTO assistant_message_types (name, description, created_at, updated_at) VALUES
('System', 'System or instruction messages that provide context and guidelines to the AI assistant', NOW(), NOW()),
('User', 'Messages sent by users as input to the AI assistant', NOW(), NOW()),
('Assistant', 'Response messages generated by the AI assistant', NOW(), NOW()),
('Action', 'Action items or next steps suggested by the AI assistant for user follow-up', NOW(), NOW())
ON CONFLICT (name) DO NOTHING;
